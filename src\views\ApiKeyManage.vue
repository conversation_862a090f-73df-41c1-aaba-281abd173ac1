<script setup lang="ts">
import { ref } from 'vue'
import { useApiKeyStore } from '@/stores/apiKey'
import { useLanguageStore } from '@/stores/language'

const apiKeyStore = useApiKeyStore()
const languageStore = useLanguageStore()
const newKeyName = ref('')
const dialogVisible = ref(false)
const editDialogVisible = ref(false)
const editingKey = ref({ id: '', name: '' })

const createNewKey = () => {
  if (newKeyName.value.trim()) {
    apiKeyStore.createKey(newKeyName.value.trim())
    newKeyName.value = ''
    dialogVisible.value = false
  }
}

const editKeyName = (key: any) => {
  editingKey.value = { id: key.id, name: key.name }
  editDialogVisible.value = true
}

const updateKeyName = () => {
  if (editingKey.value.name.trim()) {
    const key = apiKeyStore.apiKeys.find(k => k.id === editingKey.value.id)
    if (key) key.name = editingKey.value.name.trim()
    editDialogVisible.value = false
  }
}
</script>

<template>
  <div class="api-key-manage">
    <div class="header">
      <h2>{{ languageStore.t('apiKey.title') }}</h2>
      <el-button type="primary" @click="dialogVisible = true">{{ languageStore.t('apiKey.createKey') }}</el-button>
    </div>
    
    <div class="key-list">
      <el-table :data="apiKeyStore.apiKeys" style="width: 100%" border>
        <el-table-column prop="name" :label="languageStore.t('apiKey.name')" width="180" />
        <el-table-column :label="languageStore.t('apiKey.key')">
          <template #default="scope">
            <div class="key-display">
              <span v-if="apiKeyStore.showKey[scope.row.id]">{{ scope.row.key }}</span>
              <span v-else>{{ scope.row.key.replace(/./g, '*') }}</span>
              <el-button 
                :icon="apiKeyStore.showKey[scope.row.id] ? 'el-icon-view' : 'el-icon-lock'" 
                circle 
                size="small"
                @click="apiKeyStore.toggleKeyVisibility(scope.row.id)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created" :label="languageStore.t('apiKey.created')" width="120" />
        <el-table-column prop="lastUsed" :label="languageStore.t('apiKey.lastUsed')" width="120" />
        <el-table-column :label="languageStore.t('apiKey.actions')" width="200">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="editKeyName(scope.row)"
            >
              {{ languageStore.t('apiKey.edit') }}
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="apiKeyStore.copyToClipboard(scope.row.key)"
            >
              {{ languageStore.t('apiKey.copy') }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="apiKeyStore.deleteKey(scope.row.id)"
            >
              {{ languageStore.t('apiKey.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 创建Key对话框 -->
    <el-dialog v-model="dialogVisible" :title="languageStore.t('apiKey.createNewKey')" width="30%">
      <el-form>
        <el-form-item :label="languageStore.t('apiKey.keyName')">
          <el-input v-model="newKeyName" :placeholder="languageStore.t('apiKey.enterKeyName')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">{{ languageStore.t('common.cancel') }}</el-button>
        <el-button type="primary" @click="createNewKey">{{ languageStore.t('common.create') }}</el-button>
      </template>
    </el-dialog>
    
    <!-- 编辑Key对话框 -->
    <el-dialog v-model="editDialogVisible" :title="languageStore.t('apiKey.editKeyName')" width="30%">
      <el-form>
        <el-form-item :label="languageStore.t('apiKey.keyName')">
          <el-input v-model="editingKey.name" :placeholder="languageStore.t('apiKey.enterNewKeyName')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">{{ languageStore.t('common.cancel') }}</el-button>
        <el-button type="primary" @click="updateKeyName">{{ languageStore.t('common.save') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.api-key-manage {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.key-list {
  margin-top: 20px;
}

.key-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .api-key-manage {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header h2 {
    font-size: 20px;
    margin: 0;
  }

  .key-list {
    margin-top: 16px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .el-table {
    min-width: 700px;
    font-size: 12px;
  }

  .el-table .el-table__cell {
    padding: 6px 4px;
  }

  .key-display {
    gap: 6px;
  }

  .key-display span {
    font-size: 11px;
    word-break: break-all;
  }

  .el-button--small {
    padding: 4px 6px;
    font-size: 10px;
  }

  /* 对话框优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 2.5%;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .api-key-manage {
    padding: 20px;
  }

  .header h2 {
    font-size: 22px;
  }

  .el-table {
    font-size: 14px;
  }

  .el-dialog {
    width: 80% !important;
  }
}

/* 表格操作按钮优化 */
@media (max-width: 1023px) {
  .key-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .key-display .el-button {
    align-self: flex-end;
  }
}
</style>
