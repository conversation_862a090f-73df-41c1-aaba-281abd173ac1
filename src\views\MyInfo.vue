<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const editNickname = ref(false)
const newNickname = ref(userStore.nickname)
const editPhone = ref(false)
const newPhone = ref(userStore.phone)

const saveNickname = () => {
  if (newNickname.value.trim()) {
    userStore.updateNickname(newNickname.value)
    editNickname.value = false
  }
}

const savePhone = () => {
  if (newPhone.value.trim()) {
    userStore.updatePhone(newPhone.value)
    editPhone.value = false
  }
}
</script>

<template>
  <div class="my-info">
    <h2>我的信息</h2>
    <div class="info-card">
      <div class="avatar-section">
        <img src="../assets/vue.svg" alt="头像" class="avatar" />
        <el-button size="small">更换头像</el-button>
      </div>
      <div class="info-section">
        <div class="info-item">
          <label>昵称：</label>
          <span v-if="!editNickname">{{ userStore.nickname }}</span>
          <el-input v-else v-model="newNickname" size="small" style="width: 200px" />
          <el-button 
            v-if="!editNickname" 
            size="small" 
            @click="editNickname = true; newNickname = userStore.nickname"
          >
            修改
          </el-button>
          <div v-else>
            <el-button type="primary" size="small" @click="saveNickname">保存</el-button>
            <el-button size="small" @click="editNickname = false">取消</el-button>
          </div>
        </div>
        <div class="info-item">
          <label>手机号：</label>
          <span v-if="!editPhone">{{ userStore.phone }}</span>
          <el-input v-else v-model="newPhone" size="small" style="width: 200px" />
          <el-button 
            v-if="!editPhone" 
            size="small" 
            @click="editPhone = true; newPhone = userStore.phone"
          >
            修改
          </el-button>
          <div v-else>
            <el-button type="primary" size="small" @click="savePhone">保存</el-button>
            <el-button size="small" @click="editPhone = false">取消</el-button>
          </div>
        </div>
        <div class="info-item">
          <label>账户ID：</label>
          <span>{{ userStore.userId }}</span>
        </div>
        <div class="info-item">
          <label>账户余额：</label>
          <span class="balance">¥{{ userStore.balance.toFixed(2) }}</span>
          <el-button type="primary" size="small" @click="$router.push('/recharge')">充值</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.my-info {
  padding: 20px;
}

.info-card {
  display: flex;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40px;
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 10px;
}

.info-section {
  flex-grow: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.info-item label {
  width: 100px;
  font-weight: bold;
}

.info-item span {
  flex-grow: 1;
}

.info-item button {
  margin-left: 10px;
}

.balance {
  font-weight: bold;
  color: var(--theme-primary);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .my-info {
    padding: 16px;
  }

  .info-card {
    flex-direction: column;
    padding: 16px;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 20px;
    align-items: center;
  }

  .avatar {
    width: 80px;
    height: 80px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-item label {
    width: auto;
    margin-bottom: 4px;
  }

  .info-item span {
    width: 100%;
  }

  .info-item button {
    margin-left: 0;
    margin-top: 8px;
  }

  .info-item div {
    display: flex;
    gap: 8px;
    width: 100%;
  }

  .info-item div .el-button {
    flex: 1;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .my-info {
    padding: 20px;
  }

  .info-card {
    padding: 20px;
  }

  .avatar {
    width: 90px;
    height: 90px;
  }
}
</style>
