<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { ref, onMounted, onUnmounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'

import UserMenu from '@/components/UserMenu.vue'
import UserFeedback from '@/components/UserFeedback.vue'
import AboutUs from '@/components/AboutUs.vue'

// 主题管理
const themeStore = useThemeStore()
const userStore = useUserStore()

// 响应式状态管理
const isMobile = ref(false)
const isTablet = ref(false)
const sidebarVisible = ref(false)

// 用户菜单状态
const userMenuVisible = ref(false)
const userMenuPosition = ref({ x: 0, y: 0 })
const feedbackVisible = ref(false)
const aboutVisible = ref(false)

// 检测设备类型
const checkDevice = () => {
  const width = window.innerWidth
  isMobile.value = width < 768
  isTablet.value = width >= 768 && width < 1024

  // 在移动端默认隐藏侧边栏
  if (isMobile.value) {
    sidebarVisible.value = false
  } else {
    sidebarVisible.value = true
  }
}

// 切换侧边栏显示状态
const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value
}

// 在移动端点击导航项后自动隐藏侧边栏
const handleNavClick = () => {
  if (isMobile.value) {
    sidebarVisible.value = false
  }
}

// 用户菜单处理函数
const showUserMenu = (event: MouseEvent) => {
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  // 获取侧边栏的位置信息，确保菜单与侧边栏完全对齐
  const sidebar = document.querySelector('.sidebar') as HTMLElement
  const sidebarRect = sidebar?.getBoundingClientRect()

  userMenuPosition.value = {
    x: sidebarRect?.left || 0,  // 使用侧边栏的左边缘位置
    y: rect.top
  }
  userMenuVisible.value = true
}

const closeUserMenu = () => {
  userMenuVisible.value = false
}

const showFeedback = () => {
  feedbackVisible.value = true
}

const showAbout = () => {
  aboutVisible.value = true
}

// 头像加载错误处理
const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGNUY1RjUiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAxMkM5Ljc5IDEyIDggMTAuMjEgOCA4UzkuNzkgNDEyIDRTMTQuMjEgNiAxNiA4UzEyIDEwLjIxIDEyIDEyWk0xMiAxNEMxNS4zMSAxNCAyMCAxNS43OSAyMCAxOVYyMEg0VjE5QzQgMTUuNzkgOC42OSAxNCAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHN2Zz4KPHN2Zz4='
}

onMounted(() => {
  // 加载保存的主题
  themeStore.loadThemeFromStorage()

  checkDevice()
  window.addEventListener('resize', checkDevice)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkDevice)
})
</script>

<template>
  <div class="app-container" :class="{ 'mobile': isMobile, 'tablet': isTablet }">
    <!-- 顶部导航栏 -->
    <header class="top-header">
      <div class="header-left">
        <!-- 移动端菜单按钮 -->
        <button
          v-if="isMobile || isTablet"
          class="menu-button"
          @click="toggleSidebar"
          :class="{ 'active': sidebarVisible }"
        >
          <span class="hamburger-line"></span>
          <span class="hamburger-line"></span>
          <span class="hamburger-line"></span>
        </button>

        <div class="logo">
          <span class="logo-text">deepseek</span>
          <span class="beta-tag">开发平台</span>
        </div>
      </div>


    </header>

    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && sidebarVisible"
      class="sidebar-overlay"
      @click="sidebarVisible = false"
    ></div>

    <!-- 主体内容区域 -->
    <div class="main-layout">
      <!-- 左侧边栏 -->
      <aside
        class="sidebar"
        :class="{
          'sidebar-visible': sidebarVisible,
          'sidebar-hidden': !sidebarVisible,
          'sidebar-mobile': isMobile,
          'sidebar-tablet': isTablet
        }"
      >
        <nav class="sidebar-nav">
          <RouterLink
            to="/usage-info"
            class="nav-item"
            @click="handleNavClick"
          >
            <i class="icon">📊</i>
            <span>用量信息</span>
          </RouterLink>
          <RouterLink
            to="/api-key-manage"
            class="nav-item"
            @click="handleNavClick"
          >
            <i class="icon">🔑</i>
            <span>API keys</span>
          </RouterLink>
          <RouterLink
            to="/recharge"
            class="nav-item"
            @click="handleNavClick"
          >
            <i class="icon">💰</i>
            <span>充值</span>
          </RouterLink>
          <RouterLink
            to="/bill-manage"
            class="nav-item"
            @click="handleNavClick"
          >
            <i class="icon">📋</i>
            <span>账单</span>
          </RouterLink>
        </nav>

        <div class="sidebar-footer">
          <div class="help-link">
            <span>网页版免费体验</span>
            <i class="arrow">↗</i>
          </div>

          <!-- 用户信息区域 -->
          <div class="user-info" @click="showUserMenu">
            <div class="user-avatar">
              <img src="/avatar-placeholder.svg" alt="用户头像" @error="handleAvatarError" />
            </div>
            <div class="user-details">
              <div class="user-name">{{ userStore.nickname }}</div>
              <div class="user-phone">{{ userStore.phone }}</div>
            </div>
            <i class="expand-icon" :class="{ 'expanded': userMenuVisible }">▼</i>
          </div>
        </div>
      </aside>

      <!-- 右侧主内容区域 -->
      <main
        class="main-content"
        :class="{
          'content-mobile': isMobile,
          'content-tablet': isTablet,
          'content-shifted': sidebarVisible && (isMobile || isTablet)
        }"
      >
        <RouterView />
      </main>
    </div>

    <!-- 用户菜单 -->
    <UserMenu
      :visible="userMenuVisible"
      :position="userMenuPosition"
      @close="closeUserMenu"
      @show-feedback="showFeedback"
      @show-about="showAbout"
    />

    <!-- 用户反馈弹窗 -->
    <UserFeedback
      :visible="feedbackVisible"
      @close="feedbackVisible = false"
    />

    <!-- 关于我们弹窗 -->
    <AboutUs
      :visible="aboutVisible"
      @close="aboutVisible = false"
    />
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 顶部导航栏 */
.top-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 16px;
  height: 64px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 移动端菜单按钮 */
.menu-button {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.menu-button:hover {
  background-color: #f5f5f5;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background-color: #1a1a1a;
  margin: 2px 0;
  transition: all 0.3s ease;
  border-radius: 1px;
}

.menu-button.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-button.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.menu-button.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.beta-tag {
  background: #1a1a1a;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 移动端遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 64px;
  left: 280px;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 990;
  transition: opacity 0.3s ease;
}

/* 主体布局 */
.main-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 左侧边栏 */
.sidebar {
  width: 240px;
  background: #ffffff;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  padding: 24px 0;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 995;
}

/* 移动端侧边栏样式 */
.sidebar-mobile {
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  width: 280px;
  transform: translateX(-100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 995;
}

.sidebar-mobile.sidebar-visible {
  transform: translateX(0);
}

/* 平板端侧边栏样式 */
.sidebar-tablet {
  width: 200px;
}

.sidebar-tablet.sidebar-hidden {
  transform: translateX(-100%);
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 995;
}

.sidebar-tablet.sidebar-visible {
  transform: translateX(0);
}

.sidebar-nav {
  flex: 1;
  padding: 0 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  text-decoration: none;
  color: #666666;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: #f5f5f5;
  color: #1a1a1a;
}

.nav-item.router-link-active,
.nav-item.active {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.nav-item .icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 0;
  margin-top: auto;
}

.help-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  margin: 0;
}

.help-link:hover {
  color: #1a1a1a;
}

.arrow {
  font-size: 12px;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
  border-top: 1px solid #e5e5e5;
  width: 100%;
  box-sizing: border-box;
}

.user-info:hover {
  background-color: var(--theme-primary-light);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-phone {
  font-size: 12px;
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-icon {
  font-size: 12px;
  color: var(--color-text-muted);
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  background: #ffffff;
  overflow-y: auto;
  padding: 32px;
  transition: margin-left 0.3s ease;
}

/* 移动端主内容区域 */
.content-mobile {
  padding: 16px;
  margin-left: 0;
}

/* 平板端主内容区域 */
.content-tablet {
  padding: 24px;
}

.content-tablet.content-shifted {
  margin-left: 200px;
}

/* 响应式断点 */
@media (max-width: 767px) {
  .top-header {
    padding: 0 16px;
  }

  .logo-text {
    font-size: 18px;
  }

  .beta-tag {
    font-size: 10px;
    padding: 1px 6px;
  }

  .main-content {
    padding: 16px;
  }

  .sidebar-nav {
    padding: 0 12px;
  }

  .nav-item {
    padding: 14px 16px;
    font-size: 16px;
  }

  .nav-item .icon {
    font-size: 18px;
    width: 24px;
  }

  .help-link {
    margin: 0;
    padding: 8px 16px;
  }

  .user-info {
    padding: 14px 16px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .top-header {
    padding: 0 20px;
  }

  .main-content {
    padding: 24px;
  }

  .sidebar {
    width: 200px;
  }
}

@media (min-width: 1024px) {
  .menu-button {
    display: none;
  }

  .sidebar {
    position: static;
    transform: none !important;
  }

  .main-content {
    padding: 32px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1440px) {
  .main-content {
    padding: 40px;
  }

  .sidebar {
    width: 260px;
  }
}
</style>
