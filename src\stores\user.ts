import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    balance: 0,
    nickname: '用户昵称',
    phone: '138****8888',
    userId: 'USER_' + Math.random().toString(36).substring(2, 11).toUpperCase()
  }),
  actions: {
    updateBalance(amount: number) {
      this.balance += amount
    },
    updateNickname(newNickname: string) {
      this.nickname = newNickname
    },
    updatePhone(newPhone: string) {
      this.phone = newPhone
    }
  }
})
