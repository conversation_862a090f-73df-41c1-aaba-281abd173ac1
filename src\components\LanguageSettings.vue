<template>
  <div class="language-settings-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="language-dialog" @click.stop>
      <div class="dialog-header">
        <h3>{{ languageStore.t('language.title') }}</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="dialog-content">
        <p class="description">{{ languageStore.t('language.description') }}</p>
        
        <div class="language-options">
          <div class="current-language">
            <span class="label">{{ languageStore.t('language.currentLanguage') }}:</span>
            <span class="value">{{ languageStore.getCurrentLanguageName() }}</span>
          </div>
          
          <div class="language-list">
            <div 
              class="language-item"
              :class="{ active: languageStore.currentLanguage === 'zh' }"
              @click="switchLanguage('zh')"
            >
              <div class="language-info">
                <span class="language-name">中文</span>
                <span class="language-desc">简体中文</span>
              </div>
              <div class="check-icon" v-if="languageStore.currentLanguage === 'zh'">✓</div>
            </div>
            
            <div 
              class="language-item"
              :class="{ active: languageStore.currentLanguage === 'en' }"
              @click="switchLanguage('en')"
            >
              <div class="language-info">
                <span class="language-name">English</span>
                <span class="language-desc">English (US)</span>
              </div>
              <div class="check-icon" v-if="languageStore.currentLanguage === 'en'">✓</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useLanguageStore, type Language } from '@/stores/language'

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

const languageStore = useLanguageStore()

const handleOverlayClick = () => {
  emit('close')
}

const switchLanguage = (lang: Language) => {
  if (lang !== languageStore.currentLanguage) {
    languageStore.setLanguage(lang)
    // 可以添加切换成功的提示
    setTimeout(() => {
      emit('close')
    }, 300)
  }
}
</script>

<style scoped>
.language-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.language-dialog {
  background: var(--color-surface);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 480px;
  max-height: 80vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-muted);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--color-border);
  color: var(--color-text-primary);
}

.dialog-content {
  padding: 24px;
}

.description {
  margin: 0 0 24px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.current-language {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: var(--theme-primary-light);
  border-radius: 8px;
  border: 1px solid var(--theme-primary);
}

.current-language .label {
  font-size: 14px;
  color: var(--theme-primary-dark);
  font-weight: 500;
}

.current-language .value {
  font-size: 14px;
  color: var(--theme-primary);
  font-weight: 600;
}

.language-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--color-surface);
}

.language-item:hover {
  border-color: var(--theme-primary);
  background: var(--theme-primary-light);
}

.language-item.active {
  border-color: var(--theme-primary);
  background: var(--theme-primary-light);
}

.language-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.language-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.language-desc {
  font-size: 13px;
  color: var(--color-text-secondary);
}

.check-icon {
  font-size: 18px;
  color: var(--theme-primary);
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-settings-overlay {
    padding: 16px;
  }
  
  .language-dialog {
    max-width: none;
    width: 100%;
  }
  
  .dialog-header {
    padding: 16px 20px;
  }
  
  .dialog-header h3 {
    font-size: 16px;
  }
  
  .dialog-content {
    padding: 20px;
  }
  
  .language-item {
    padding: 14px;
  }
  
  .language-name {
    font-size: 15px;
  }
  
  .language-desc {
    font-size: 12px;
  }
}
</style>
