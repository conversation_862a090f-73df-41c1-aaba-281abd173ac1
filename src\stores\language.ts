import { defineStore } from 'pinia'
import { ref } from 'vue'

export type Language = 'zh' | 'en'

// 中文翻译
const zhTranslations = {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    copy: '复制',
    create: '创建',
    back: '返回',
    close: '关闭',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告'
  },
  
  // 导航
  nav: {
    usageInfo: '用量信息',
    apiKeys: 'API keys',
    recharge: '充值',
    billManage: '账单',
    myInfo: '我的信息'
  },
  
  // 用户菜单
  userMenu: {
    themeSettings: '主题设置',
    personalInfo: '个人信息',
    languageSettings: '语言设置',
    feedback: '用户反馈',
    about: '关于我们',
    privacy: '隐私政策',
    logout: '退出登录',
    logoutConfirm: '确定要退出登录吗？'
  },
  
  // API Key管理
  apiKey: {
    title: 'API Key 管理',
    createKey: '创建 API Key',
    createNewKey: '创建新的API Key',
    editKeyName: '编辑Key名称',
    keyName: 'Key名称',
    keyNamePlaceholder: '请输入Key名称',
    newKeyNamePlaceholder: '请输入新的Key名称',
    name: '名称',
    key: 'Key',
    created: '创建时间',
    lastUsed: '最后使用',
    actions: '操作',
    neverUsed: '从未使用'
  },
  
  // 主题设置
  theme: {
    title: '主题设置',
    description: '自定义您的界面主题色彩，让体验更加个性化',
    presetThemes: '预设主题',
    customTheme: '自定义主题',
    themeManagement: '主题管理',
    primaryColor: '主色调',
    lightColor: '浅色调',
    darkColor: '深色调',
    applyCustomTheme: '应用自定义主题',
    reset: '重置',
    exportTheme: '导出当前主题',
    importTheme: '导入主题',
    resetToDefault: '恢复默认设置',
    resetConfirm: '确定要恢复默认主题设置吗？这将清除所有自定义配置。',
    themeFileError: '主题文件格式错误',
    usageGuide: '主题使用说明',
    presetThemeDesc: '选择我们精心设计的预设主题，一键应用到整个界面。每个主题都经过精心调色，确保最佳的视觉体验。',
    customThemeDesc: '通过自定义主题功能，您可以创建独特的个人主题。调整主色调、浅色调和深色调，系统会自动生成配套的辅助色彩。',
    importExportDesc: '您可以导出当前的主题配置为JSON文件，方便备份或分享给其他用户。同样，您也可以导入其他人分享的主题文件。',
    colorTipsDesc: '选择主色调时，建议选择饱和度适中的颜色。浅色调用于背景和高亮，深色调用于文字和边框。确保颜色对比度足够，以保证可读性。'
  },
  
  // 语言设置
  language: {
    title: '语言设置',
    description: '选择您偏好的界面语言',
    chinese: '中文',
    english: 'English',
    currentLanguage: '当前语言',
    switchSuccess: '语言切换成功'
  },
  
  // 应用标题
  app: {
    title: '用户中心',
    logo: 'deepseek',
    betaTag: '开发平台'
  }
}

// 英文翻译
const enTranslations = {
  // 通用
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    copy: 'Copy',
    create: 'Create',
    back: 'Back',
    close: 'Close',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning'
  },
  
  // 导航
  nav: {
    usageInfo: 'Usage Info',
    apiKeys: 'API Keys',
    recharge: 'Recharge',
    billManage: 'Bills',
    myInfo: 'My Info'
  },
  
  // 用户菜单
  userMenu: {
    themeSettings: 'Theme Settings',
    personalInfo: 'Personal Info',
    languageSettings: 'Language Settings',
    feedback: 'Feedback',
    about: 'About Us',
    privacy: 'Privacy Policy',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?'
  },
  
  // API Key管理
  apiKey: {
    title: 'API Key Management',
    createKey: 'Create API Key',
    createNewKey: 'Create New API Key',
    editKeyName: 'Edit Key Name',
    keyName: 'Key Name',
    keyNamePlaceholder: 'Please enter key name',
    newKeyNamePlaceholder: 'Please enter new key name',
    name: 'Name',
    key: 'Key',
    created: 'Created',
    lastUsed: 'Last Used',
    actions: 'Actions',
    neverUsed: 'Never Used'
  },
  
  // 主题设置
  theme: {
    title: 'Theme Settings',
    description: 'Customize your interface theme colors for a more personalized experience',
    presetThemes: 'Preset Themes',
    customTheme: 'Custom Theme',
    themeManagement: 'Theme Management',
    primaryColor: 'Primary Color',
    lightColor: 'Light Color',
    darkColor: 'Dark Color',
    applyCustomTheme: 'Apply Custom Theme',
    reset: 'Reset',
    exportTheme: 'Export Current Theme',
    importTheme: 'Import Theme',
    resetToDefault: 'Reset to Default',
    resetConfirm: 'Are you sure you want to reset to default theme settings? This will clear all custom configurations.',
    themeFileError: 'Theme file format error',
    usageGuide: 'Theme Usage Guide',
    presetThemeDesc: 'Choose from our carefully designed preset themes and apply them to the entire interface with one click. Each theme has been carefully color-tuned to ensure the best visual experience.',
    customThemeDesc: 'With the custom theme feature, you can create unique personal themes. Adjust primary, light, and dark colors, and the system will automatically generate matching auxiliary colors.',
    importExportDesc: 'You can export your current theme configuration as a JSON file for backup or sharing with other users. You can also import theme files shared by others.',
    colorTipsDesc: 'When choosing primary colors, it is recommended to select colors with moderate saturation. Light colors are used for backgrounds and highlights, dark colors for text and borders. Ensure sufficient color contrast to guarantee readability.'
  },
  
  // 语言设置
  language: {
    title: 'Language Settings',
    description: 'Select your preferred interface language',
    chinese: '中文',
    english: 'English',
    currentLanguage: 'Current Language',
    switchSuccess: 'Language switched successfully'
  },
  
  // 应用标题
  app: {
    title: 'User Center',
    logo: 'deepseek',
    betaTag: 'Dev Platform'
  }
}

export const useLanguageStore = defineStore('language', () => {
  // 当前语言
  const currentLanguage = ref<Language>('zh')
  
  // 翻译对象
  const translations = {
    zh: zhTranslations,
    en: enTranslations
  }
  
  // 获取翻译文本
  const t = (key: string): string => {
    const keys = key.split('.')
    let value: any = translations[currentLanguage.value]
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        console.warn(`Translation key not found: ${key}`)
        return key
      }
    }
    
    return typeof value === 'string' ? value : key
  }
  
  // 切换语言
  const setLanguage = (lang: Language) => {
    currentLanguage.value = lang
    saveLanguageToStorage()
  }
  
  // 保存语言设置到本地存储
  const saveLanguageToStorage = () => {
    localStorage.setItem('user-center-language', currentLanguage.value)
  }
  
  // 从本地存储加载语言设置
  const loadLanguageFromStorage = () => {
    try {
      const saved = localStorage.getItem('user-center-language')
      if (saved && (saved === 'zh' || saved === 'en')) {
        currentLanguage.value = saved as Language
      }
    } catch (error) {
      console.error('Failed to load language from storage:', error)
    }
  }
  
  // 获取当前语言显示名称
  const getCurrentLanguageName = () => {
    return currentLanguage.value === 'zh' ? '中文' : 'English'
  }
  
  return {
    currentLanguage,
    t,
    setLanguage,
    loadLanguageFromStorage,
    getCurrentLanguageName
  }
})
