import { defineStore } from 'pinia'

export const useApiKeyStore = defineStore('apiKey', {
  state: () => ({
    apiKeys: [] as Array<{
      id: string
      name: string
      key: string
      created: string
      lastUsed: string
    }>,
    showKey: {} as Record<string, boolean>
  }),
  actions: {
    createKey(name: string) {
      const newKey = {
        id: Date.now().toString(),
        name,
        key: 'sk-' + Math.random().toString(36).substring(2, 15),
        created: new Date().toLocaleDateString(),
        lastUsed: '从未使用'
      }
      this.apiKeys.push(newKey)
      this.showKey[newKey.id] = false
    },
    toggleKeyVisibility(id: string) {
      this.showKey[id] = !this.showKey[id]
    },
    copyToClipboard(key: string) {
      navigator.clipboard.writeText(key)
    },
    deleteKey(id: string) {
      this.apiKeys = this.apiKeys.filter(k => k.id !== id)
      delete this.showKey[id]
    }
  }
})
