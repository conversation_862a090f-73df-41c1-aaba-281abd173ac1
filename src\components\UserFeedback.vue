<template>
  <div class="user-feedback-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="feedback-dialog" @click.stop>
      <div class="dialog-header">
        <h3>用户反馈</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="dialog-content">
        <p class="feedback-description">
          使用过程中遇到了什么问题？
        </p>
        
        <form @submit.prevent="submitFeedback">
          <div class="form-group">
            <textarea 
              v-model="feedbackText"
              placeholder="请描述您遇到的问题或建议..."
              class="feedback-textarea"
              rows="6"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label class="contact-label">联系方式（可选）</label>
            <input 
              v-model="contactInfo"
              type="text"
              placeholder="邮箱或手机号，方便我们联系您"
              class="contact-input"
            />
          </div>
          
          <div class="form-actions">
            <button type="button" class="cancel-btn" @click="$emit('close')">
              取消
            </button>
            <button type="submit" class="submit-btn" :disabled="!feedbackText.trim()">
              提交反馈
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

const feedbackText = ref('')
const contactInfo = ref('')

const handleOverlayClick = () => {
  emit('close')
}

const submitFeedback = () => {
  if (!feedbackText.value.trim()) return
  
  // 这里可以添加提交反馈的逻辑
  console.log('反馈内容:', feedbackText.value)
  console.log('联系方式:', contactInfo.value)
  
  // 模拟提交成功
  alert('反馈提交成功，感谢您的建议！')
  
  // 清空表单并关闭对话框
  feedbackText.value = ''
  contactInfo.value = ''
  emit('close')
}
</script>

<style scoped>
.user-feedback-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.feedback-dialog {
  background: var(--color-surface);
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-border);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.dialog-content {
  padding: 24px;
}

.feedback-description {
  margin: 0 0 20px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 20px;
}

.feedback-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 120px;
  transition: border-color 0.2s ease;
}

.feedback-textarea:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-primary-light);
}

.contact-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.contact-input {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.contact-input:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-primary-light);
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

.cancel-btn {
  padding: 10px 20px;
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

.submit-btn {
  padding: 10px 20px;
  background: var(--theme-primary);
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.submit-btn:hover:not(:disabled) {
  background: var(--theme-primary-dark);
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-feedback-overlay {
    padding: 16px;
  }
  
  .feedback-dialog {
    max-width: none;
  }
  
  .dialog-header {
    padding: 16px 20px 12px;
  }
  
  .dialog-content {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column-reverse;
  }
  
  .cancel-btn,
  .submit-btn {
    width: 100%;
    padding: 12px;
  }
}
</style>
