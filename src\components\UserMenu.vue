<template>
  <div class="user-menu-container" v-if="visible">
    <!-- 遮罩层 -->
    <div class="menu-overlay" @click="$emit('close')"></div>
    
    <!-- 菜单内容 -->
    <div class="user-menu" :style="menuStyle">
      <!-- 用户信息头部 -->
      <div class="menu-header">
        <div class="user-avatar">
          <img src="/avatar-placeholder.svg" alt="用户头像" />
        </div>
        <div class="user-details">
          <div class="user-name">{{ userStore.nickname }}</div>
          <div class="user-phone">{{ userStore.phone }}</div>
        </div>
      </div>
      
      <!-- 菜单分隔线 -->
      <div class="menu-divider"></div>
      
      <!-- 菜单项 -->
      <div class="menu-items">
        <!-- 设置相关 -->
        <div class="menu-section">
          <div class="menu-item" @click="handleThemeSettings">
            <div class="menu-icon">🎨</div>
            <span class="menu-label">{{ languageStore.t('userMenu.themeSettings') }}</span>
            <div class="menu-arrow">›</div>
          </div>

          <div class="menu-item" @click="handlePersonalInfo">
            <div class="menu-icon">👤</div>
            <span class="menu-label">{{ languageStore.t('userMenu.personalInfo') }}</span>
            <div class="menu-arrow">›</div>
          </div>

          <div class="menu-item" @click="handleLanguage">
            <div class="menu-icon">🌐</div>
            <span class="menu-label">{{ languageStore.t('userMenu.languageSettings') }}</span>
            <div class="menu-value">{{ languageStore.getCurrentLanguageName() }}</div>
            <div class="menu-arrow">›</div>
          </div>
        </div>
        
        <!-- 分隔线 -->
        <div class="menu-divider"></div>
        
        <!-- 帮助与反馈 -->
        <div class="menu-section">
          <div class="menu-item" @click="handleFeedback">
            <div class="menu-icon">💬</div>
            <span class="menu-label">{{ languageStore.t('userMenu.feedback') }}</span>
            <div class="menu-arrow">›</div>
          </div>

          <div class="menu-item" @click="handleAbout">
            <div class="menu-icon">📄</div>
            <span class="menu-label">{{ languageStore.t('userMenu.about') }}</span>
            <div class="menu-arrow">›</div>
          </div>

          <div class="menu-item" @click="handlePrivacy">
            <div class="menu-icon">🔒</div>
            <span class="menu-label">{{ languageStore.t('userMenu.privacy') }}</span>
            <div class="menu-arrow">›</div>
          </div>
        </div>
        
        <!-- 分隔线 -->
        <div class="menu-divider"></div>
        
        <!-- 退出登录 -->
        <div class="menu-section">
          <div class="menu-item logout-item" @click="handleLogout">
            <div class="menu-icon">🚪</div>
            <span class="menu-label">{{ languageStore.t('userMenu.logout') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useLanguageStore } from '@/stores/language'
import { useRouter } from 'vue-router'

const props = defineProps<{
  visible: boolean
  position: { x: number; y: number }
}>()

const emit = defineEmits<{
  close: []
  'show-feedback': []
  'show-about': []
  'navigate-to-theme': []
  'show-language': []
}>()

const userStore = useUserStore()
const languageStore = useLanguageStore()
const router = useRouter()

// 计算菜单位置
const menuStyle = computed(() => {
  const sidebarWidth = window.innerWidth >= 1024 ? 260 : (window.innerWidth >= 768 ? 200 : 280)
  const menuHeight = 400 // 估算菜单高度

  // 菜单与侧边栏完全等宽，左边缘对齐
  const left = props.position.x
  const bottom = window.innerHeight - props.position.y + 10

  // 确保菜单不会超出顶部边界
  if (window.innerHeight - bottom < menuHeight + 20) {
    // 如果上方空间不够，改为向下弹出
    return {
      left: `${left}px`,
      top: `${props.position.y + 60}px`,
      width: `${sidebarWidth}px`
    }
  }

  return {
    left: `${left}px`,
    bottom: `${bottom}px`,
    width: `${sidebarWidth}px`
  }
})

// 处理主题设置
const handleThemeSettings = () => {
  emit('close')
  emit('navigate-to-theme')
}

// 处理个人信息
const handlePersonalInfo = () => {
  emit('close')
  router.push('/my-info')
}

// 处理语言设置
const handleLanguage = () => {
  emit('close')
  emit('show-language')
}

// 处理用户反馈
const handleFeedback = () => {
  emit('close')
  emit('show-feedback')
}

// 处理关于我们
const handleAbout = () => {
  emit('close')
  emit('show-about')
}

// 处理隐私政策
const handlePrivacy = () => {
  emit('close')
  emit('show-about')
}

// 处理退出登录
const handleLogout = () => {
  emit('close')
  if (confirm(languageStore.t('userMenu.logoutConfirm'))) {
    // 这里添加退出登录逻辑
    console.log('退出登录')
  }
}
</script>

<style scoped>
.user-menu-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1500;
}

.menu-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
}

.user-menu {
  position: absolute;
  background: var(--color-surface);
  border-radius: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border-right: 1px solid var(--color-border);
  border-left: none;
  border-top: 1px solid var(--color-border);
  border-bottom: none;
  overflow: hidden;
  animation: menuSlideUp 0.2s ease-out;
  transform-origin: bottom left;
}

@keyframes menuSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.menu-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-phone {
  font-size: 14px;
  color: var(--theme-primary-dark);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-items {
  padding: 8px 0;
}

.menu-section {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text-primary);
}

.menu-item:hover {
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.menu-item.logout-item {
  color: var(--color-danger);
}

.menu-item.logout-item:hover {
  background-color: #fee2e2;
  color: var(--color-danger);
}

.menu-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.menu-label {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.menu-value {
  font-size: 13px;
  color: var(--color-text-secondary);
  margin-right: 4px;
}

.menu-arrow {
  font-size: 16px;
  color: var(--color-text-muted);
  flex-shrink: 0;
}

.menu-divider {
  height: 1px;
  background: var(--color-border);
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .menu-header {
    padding: 14px 16px;
  }

  .user-avatar {
    width: 44px;
    height: 44px;
  }

  .menu-item {
    padding: 14px 16px;
  }

  .menu-label {
    font-size: 15px;
  }
}
</style>
