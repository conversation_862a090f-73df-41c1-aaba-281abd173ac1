<script setup lang="ts">
import { ref } from 'vue'
import { useLanguageStore } from '@/stores/language'

const languageStore = useLanguageStore()

const usageData = ref([
  { date: '10-01', usage: 120 },
  { date: '10-02', usage: 200 },
  { date: '10-03', usage: 150 },
  { date: '10-04', usage: 300 },
  { date: '10-05', usage: 280 },
  { date: '10-06', usage: 400 }
])

const activeKey = ref('daily')
</script>

<template>
  <div class="usage-info">
    <h2>{{ languageStore.t('nav.usageInfo') }}</h2>
    <p style="color: #666; font-size: 14px; margin-bottom: 20px;">
      当前语言: {{ languageStore.getCurrentLanguageName() }} ({{ languageStore.currentLanguage }})
    </p>
    
    <el-tabs v-model="activeKey" class="usage-tabs">
      <el-tab-pane label="每日用量" name="daily">
        <div class="chart-container">
          <div class="chart">
            <div 
              v-for="(item, index) in usageData" 
              :key="index" 
              class="bar"
              :style="{ height: item.usage / 5 + 'px' }"
            >
              <span class="bar-value">{{ item.usage }}</span>
              <span class="bar-label">{{ item.date }}</span>
            </div>
          </div>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="每周用量" name="weekly">
        <div class="chart-container">
          <p>每周用量图表将在这里显示</p>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="每月用量" name="monthly">
        <div class="chart-container">
          <p>每月用量图表将在这里显示</p>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <div class="usage-summary">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="summary-card">
            <div class="summary-value">1,450</div>
            <div class="summary-label">今日用量</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-card">
            <div class="summary-value">9,820</div>
            <div class="summary-label">本周用量</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-card">
            <div class="summary-value">32,560</div>
            <div class="summary-label">本月用量</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.usage-info {
  padding: 20px;
}

.chart-container {
  margin-top: 20px;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  height: 300px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.chart {
  display: flex;
  align-items: flex-end;
  height: 100%;
  gap: 30px;
}

.bar {
  width: 50px;
  background: #409eff;
  position: relative;
  border-radius: 4px 4px 0 0;
}

.bar-value {
  position: absolute;
  top: -25px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 12px;
}

.bar-label {
  position: absolute;
  bottom: -25px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 12px;
}

.usage-summary {
  margin-top: 30px;
}

.summary-card {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.summary-label {
  margin-top: 10px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .usage-info {
    padding: 16px;
  }

  .usage-info h2 {
    font-size: 20px;
  }

  .usage-tabs {
    font-size: 14px;
  }

  .chart-container {
    height: 200px;
    padding: 16px;
    margin-top: 16px;
  }

  .chart {
    gap: 15px;
  }

  .bar {
    width: 30px;
  }

  .bar-value,
  .bar-label {
    font-size: 10px;
  }

  .usage-summary {
    margin-top: 20px;
  }

  .summary-card {
    padding: 16px;
    margin-bottom: 12px;
  }

  .summary-value {
    font-size: 20px;
  }

  .summary-label {
    font-size: 12px;
    margin-top: 8px;
  }

  /* 移动端网格布局 */
  .el-row {
    margin: 0 -6px;
  }

  .el-col {
    padding: 0 6px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .usage-info {
    padding: 20px;
  }

  .chart-container {
    height: 250px;
    padding: 18px;
  }

  .chart {
    gap: 20px;
  }

  .bar {
    width: 40px;
  }

  .summary-card {
    padding: 18px;
  }

  .summary-value {
    font-size: 22px;
  }
}

/* 图表响应式优化 */
@media (max-width: 480px) {
  .chart {
    gap: 10px;
    justify-content: space-around;
  }

  .bar {
    width: 25px;
  }

  .bar-value,
  .bar-label {
    font-size: 9px;
  }

  .summary-value {
    font-size: 18px;
  }
}

/* 横屏优化 */
@media (max-height: 500px) and (orientation: landscape) {
  .chart-container {
    height: 150px;
  }

  .usage-summary {
    margin-top: 16px;
  }
}
</style>
